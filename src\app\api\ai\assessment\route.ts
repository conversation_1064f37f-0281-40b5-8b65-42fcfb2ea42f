import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { generateAssessmentWithGemini } from '@/lib/gemini';
import { authOptions } from '@/lib/auth';
import { Level, Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const { 
      quizResults, 
      language = 'en',
      requestType = 'analysis' // 'analysis' or 'recommendations'
    } = await request.json();

    if (!quizResults || !Array.isArray(quizResults) || quizResults.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Quiz results are required for assessment' },
        { status: 400 }
      );
    }

    // Get user data for personalized assessment
    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Analyze quiz results
    const totalQuestions = quizResults.length;
    const correctAnswers = quizResults.filter((result: any) => result.isCorrect).length;
    const accuracy = (correctAnswers / totalQuestions) * 100;

    // Categorize mistakes by type
    const mistakes = quizResults.filter((result: any) => !result.isCorrect);
    const mistakesByLevel = mistakes.reduce((acc: any, mistake: any) => {
      const level = mistake.level || 'unknown';
      if (!acc[level]) acc[level] = [];
      acc[level].push(mistake);
      return acc;
    }, {});

    // Generate AI assessment
    const assessment = await generateAssessmentWithGemini({
      userId: session.user.id,
      quizResults,
      userLevel: user.level || 'beginner',
      language,
      accuracy,
      mistakesByLevel,
      requestType
    });

    // Update user statistics
    const newStats = {
      totalQuestions: (user.stats?.totalQuestions || 0) + totalQuestions,
      correctAnswers: (user.stats?.correctAnswers || 0) + correctAnswers,
      quizzesTaken: (user.stats?.quizzesTaken || 0) + 1,
      lastQuizDate: new Date(),
      averageAccuracy: Math.round(
        ((user.stats?.correctAnswers || 0) + correctAnswers) / 
        ((user.stats?.totalQuestions || 0) + totalQuestions) * 100
      )
    };

    await User.findByIdAndUpdate(session.user.id, {
      $set: {
        stats: newStats,
        lastActive: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      assessment,
      userStats: newStats
    });
  } catch (error) {
    console.error('Error generating assessment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate assessment' },
      { status: 500 }
    );
  }
}
