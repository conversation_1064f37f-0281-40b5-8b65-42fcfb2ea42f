'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import AdSense from '@/components/AdSense';

interface LessonSection {
  id: string;
  title: string;
  type: 'explanation' | 'exercise' | 'summary';
  content?: string;
  examples?: string[];
  exercises?: Array<{
    id: string;
    type: 'multiple-choice' | 'fill-blank' | 'true-false';
    question: string;
    options?: string[];
    answer?: string;
    correctAnswer?: number;
    explanation: string;
  }>;
  keyPoints?: string[];
  duration: number;
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  type: string;
  topic: string;
  duration: number;
  objectives: string[];
  sections: LessonSection[];
  vocabulary: Array<{
    word: string;
    definition: string;
    example: string;
    pronunciation?: string;
  }>;
  homework?: string[];
  additionalResources?: string[];
  tips?: string[];
}

export default function LessonsClient() {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [selectedLevel, setSelectedLevel] = useState<string>('beginner');
  const [selectedType, setSelectedType] = useState<string>('grammar');
  const [topic, setTopic] = useState<string>('');
  const [duration, setDuration] = useState<number>(30);
  const [isGenerating, setIsGenerating] = useState(false);
  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({});
  const [showVocabulary, setShowVocabulary] = useState(false);

  const lessonTypes = [
    { id: 'grammar', name: 'Grammar', icon: '📝', description: 'Grammar rules and structures' },
    { id: 'vocabulary', name: 'Vocabulary', icon: '📚', description: 'Vocabulary building and word usage' },
    { id: 'conversation', name: 'Conversation', icon: '💬', description: 'Conversational skills and dialogue practice' },
    { id: 'pronunciation', name: 'Pronunciation', icon: '🗣️', description: 'Pronunciation and phonetics' },
    { id: 'writing', name: 'Writing', icon: '✍️', description: 'Writing skills and composition' },
    { id: 'listening', name: 'Listening', icon: '👂', description: 'Listening comprehension and audio skills' },
    { id: 'culture', name: 'Culture', icon: '🌍', description: 'Cultural understanding and context' }
  ];

  const topicSuggestions = [
    'Present Simple Tense',
    'Past Tense Forms',
    'Future Tense',
    'Conditional Sentences',
    'Modal Verbs',
    'Phrasal Verbs',
    'Business English',
    'Travel Vocabulary',
    'Food and Cooking',
    'Technology Terms',
    'Daily Routines',
    'Family and Relationships'
  ];

  const levels = ['beginner', 'intermediate', 'advanced'];

  const generateLesson = async () => {
    if (isGenerating || !topic.trim()) return;
    
    setIsGenerating(true);
    try {
      const response = await fetch('/api/ai/lesson', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: selectedLevel,
          language: 'en',
          topic: topic.trim(),
          lessonType: selectedType,
          duration
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        const lesson = {
          id: `ai_${Date.now()}`,
          ...data.lesson
        };
        setSelectedLesson(lesson);
        setCurrentSectionIndex(0);
        setUserAnswers({});
        setShowVocabulary(false);
      } else {
        throw new Error(data.error || 'Failed to generate lesson');
      }
    } catch (error) {
      console.error('Error generating lesson:', error);
      alert('Failed to generate lesson. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-orange-600 bg-orange-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleExerciseAnswer = (exerciseId: string, answer: any) => {
    setUserAnswers(prev => ({
      ...prev,
      [exerciseId]: answer
    }));
  };

  const nextSection = () => {
    if (selectedLesson && currentSectionIndex < selectedLesson.sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
    }
  };

  const prevSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
    }
  };

  if (selectedLesson) {
    const currentSection = selectedLesson.sections[currentSectionIndex];
    const progress = ((currentSectionIndex + 1) / selectedLesson.sections.length) * 100;

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={() => {
            setSelectedLesson(null);
            setCurrentSectionIndex(0);
            setUserAnswers({});
          }}
        />

        <div className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Lesson Header */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800">{selectedLesson.title}</h1>
                  <p className="text-gray-600 mt-1">{selectedLesson.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                    <span>⏱️ {selectedLesson.duration} min</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(selectedLesson.level)}`}>
                      {selectedLesson.level}
                    </span>
                    <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                      {selectedLesson.type}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setShowVocabulary(!showVocabulary)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    showVocabulary 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  📚 Vocabulary
                </button>
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{currentSectionIndex + 1} of {selectedLesson.sections.length}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              {/* Vocabulary Panel */}
              {showVocabulary && (
                <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-3">Lesson Vocabulary:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {selectedLesson.vocabulary.map((item, index) => (
                      <div key={index} className="bg-white p-3 rounded-lg">
                        <div className="font-medium text-blue-700 flex items-center space-x-2">
                          <span>{item.word}</span>
                          {item.pronunciation && (
                            <span className="text-xs text-gray-500">/{item.pronunciation}/</span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 mb-1">{item.definition}</div>
                        <div className="text-xs text-gray-500 italic">"{item.example}"</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Current Section */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-800">{currentSection.title}</h2>
                <span className="text-sm text-gray-500">
                  ⏱️ {currentSection.duration} min
                </span>
              </div>

              {currentSection.type === 'explanation' && (
                <div className="prose max-w-none">
                  <div className="text-gray-700 leading-relaxed mb-6">
                    {currentSection.content && (
                      <MarkdownRenderer
                        content={currentSection.content}
                        className="text-gray-700 leading-relaxed"
                      />
                    )}
                  </div>

                  {currentSection.examples && currentSection.examples.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Examples:</h3>
                      <div className="space-y-2">
                        {currentSection.examples.map((example, index) => (
                          <div key={index} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                            <MarkdownRenderer
                              content={example}
                              className="text-gray-700"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {currentSection.type === 'exercise' && (
                <div className="space-y-6">
                  {currentSection.exercises?.map((exercise, index) => (
                    <div key={exercise.id} className="p-6 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-800 mb-4">
                        Exercise {index + 1}:
                        <MarkdownRenderer
                          content={exercise.question}
                          className="inline ml-2"
                        />
                      </h4>

                      {exercise.type === 'multiple-choice' && (
                        <div className="space-y-2">
                          {exercise.options?.map((option, optionIndex) => {
                            const isSelected = userAnswers[exercise.id] === optionIndex;
                            const isCorrect = optionIndex === exercise.correctAnswer;
                            const showResult = userAnswers[exercise.id] !== undefined;

                            return (
                              <button
                                key={optionIndex}
                                onClick={() => handleExerciseAnswer(exercise.id, optionIndex)}
                                className={`w-full text-left p-3 rounded-lg border transition-colors ${
                                  showResult && isCorrect
                                    ? 'bg-green-100 border-green-500 text-green-700'
                                    : showResult && isSelected && !isCorrect
                                    ? 'bg-red-100 border-red-500 text-red-700'
                                    : isSelected
                                    ? 'bg-blue-100 border-blue-500 text-blue-700'
                                    : 'bg-white border-gray-300 hover:bg-gray-100'
                                }`}
                              >
                                <MarkdownRenderer
                                  content={option}
                                  className="text-inherit"
                                />
                              </button>
                            );
                          })}
                        </div>
                      )}

                      {exercise.type === 'true-false' && (
                        <div className="space-y-2">
                          {['True', 'False'].map((option, optionIndex) => {
                            const isSelected = userAnswers[exercise.id] === optionIndex;
                            const isCorrect = optionIndex === exercise.correctAnswer;
                            const showResult = userAnswers[exercise.id] !== undefined;

                            return (
                              <button
                                key={optionIndex}
                                onClick={() => handleExerciseAnswer(exercise.id, optionIndex)}
                                className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 font-medium ${
                                  showResult && isCorrect
                                    ? 'bg-green-100 border-green-500 text-green-700 shadow-md'
                                    : showResult && isSelected && !isCorrect
                                    ? 'bg-red-100 border-red-500 text-red-700 shadow-md'
                                    : isSelected
                                    ? 'bg-blue-100 border-blue-500 text-blue-700 shadow-md'
                                    : 'bg-white border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                                }`}
                              >
                                <div className="flex items-center space-x-3">
                                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-bold ${
                                    showResult && isCorrect
                                      ? 'bg-green-500 border-green-500 text-white'
                                      : showResult && isSelected && !isCorrect
                                      ? 'bg-red-500 border-red-500 text-white'
                                      : isSelected
                                      ? 'bg-blue-500 border-blue-500 text-white'
                                      : 'border-gray-400 text-gray-600'
                                  }`}>
                                    {optionIndex === 0 ? 'T' : 'F'}
                                  </div>
                                  <span className="text-lg">{option}</span>
                                  {showResult && isCorrect && (
                                    <span className="ml-auto text-green-600">✓</span>
                                  )}
                                  {showResult && isSelected && !isCorrect && (
                                    <span className="ml-auto text-red-600">✗</span>
                                  )}
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      )}

                      {exercise.type === 'fill-blank' && (
                        <div className="space-y-3">
                          <input
                            type="text"
                            value={userAnswers[exercise.id] || ''}
                            onChange={(e) => handleExerciseAnswer(exercise.id, e.target.value)}
                            placeholder="Type your answer here..."
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                          />
                          {userAnswers[exercise.id] && (
                            <div className={`p-4 rounded-lg border-l-4 ${
                              userAnswers[exercise.id].toLowerCase().trim() === exercise.answer?.toLowerCase().trim()
                                ? 'bg-green-50 text-green-700 border-green-500'
                                : 'bg-red-50 text-red-700 border-red-500'
                            }`}>
                              <div className="flex items-center space-x-2">
                                <span className="text-xl">
                                  {userAnswers[exercise.id].toLowerCase().trim() === exercise.answer?.toLowerCase().trim()
                                    ? '✅' : '❌'
                                  }
                                </span>
                                <span className="font-medium">
                                  {userAnswers[exercise.id].toLowerCase().trim() === exercise.answer?.toLowerCase().trim()
                                    ? 'Correct!'
                                    : `Incorrect. The correct answer is: ${exercise.answer}`
                                  }
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {userAnswers[exercise.id] !== undefined && (
                        <div className="mt-4 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-lg">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                              <span className="text-white text-xs">💡</span>
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-blue-800 mb-2">Explanation:</h5>
                              <MarkdownRenderer
                                content={exercise.explanation}
                                className="text-blue-700"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {currentSection.type === 'summary' && (
                <div className="prose max-w-none">
                  <div className="text-gray-700 leading-relaxed mb-6">
                    {currentSection.content && (
                      <MarkdownRenderer
                        content={currentSection.content}
                        className="text-gray-700 leading-relaxed"
                      />
                    )}
                  </div>

                  {currentSection.keyPoints && currentSection.keyPoints.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-3">Key Points:</h3>
                      <ul className="space-y-3">
                        {currentSection.keyPoints.map((point, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-3 mt-1 text-lg">✓</span>
                            <div className="flex-1">
                              <MarkdownRenderer
                                content={point}
                                className="text-gray-700"
                              />
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <button
                onClick={prevSection}
                disabled={currentSectionIndex === 0}
                className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                ← Previous
              </button>

              <div className="text-center">
                <div className="text-sm text-gray-600">
                  Section {currentSectionIndex + 1} of {selectedLesson.sections.length}
                </div>
              </div>

              {currentSectionIndex < selectedLesson.sections.length - 1 ? (
                <button
                  onClick={nextSection}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors"
                >
                  Next →
                </button>
              ) : (
                <button
                  onClick={() => setSelectedLesson(null)}
                  className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  Complete Lesson ✓
                </button>
              )}
            </div>
          </div>

          {/* AdSense Banner */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <AdSense
              adSlot="2345678901"
              adFormat="auto"
              className="rounded-lg"
              style={{ display: 'block', minHeight: '100px' }}
              lazy={true}
            />
          </div>
        </div>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => window.history.back()}
      />

      <div className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4">
              {t('lessonTitle')}
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('lessonSubtitle')}
            </p>
          </div>

          {/* Lesson Generator */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-8 shadow-xl border border-white/20">
            <h3 className="text-xl font-semibold text-gray-800 mb-6">{t('createLesson')}</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* Level Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('level')}</label>
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {levels.map(level => (
                    <option key={level} value={level}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('lessonType')}</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {lessonTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
              </div>

              {/* Duration */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('duration')}</label>
                <select
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                  <option value={45}>45 minutes</option>
                  <option value={60}>60 minutes</option>
                </select>
              </div>

              {/* Generate Button */}
              <div className="flex items-end">
                <button
                  onClick={generateLesson}
                  disabled={isGenerating || !topic.trim()}
                  className="w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </div>
                  ) : (
                    '✨ Generate Lesson'
                  )}
                </button>
              </div>
            </div>

            {/* Topic Input */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lesson Topic *
              </label>
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="e.g., Present Simple Tense, Business English, Travel Vocabulary..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Topic Suggestions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Or choose from popular topics:
              </label>
              <div className="flex flex-wrap gap-2">
                {topicSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setTopic(suggestion)}
                    className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Lesson Types Overview */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-8 shadow-xl border border-white/20">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Lesson Types</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {lessonTypes.map((type) => (
                <div
                  key={type.id}
                  className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    selectedType === type.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedType(type.id)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{type.icon}</span>
                    <div>
                      <h4 className="font-medium text-gray-800">{type.name}</h4>
                      <p className="text-sm text-gray-600">{type.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* How it works */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">How AI Lessons Work:</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Choose Topic</h4>
                <p className="text-sm text-gray-600">Select your lesson topic and preferences</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">2</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">AI Creates</h4>
                <p className="text-sm text-gray-600">AI generates comprehensive lesson content</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">3</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Interactive Learning</h4>
                <p className="text-sm text-gray-600">Study with explanations and exercises</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-orange-600 font-bold">4</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Practice & Master</h4>
                <p className="text-sm text-gray-600">Complete exercises and track progress</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />

      <Footer />
    </div>
  );
}
