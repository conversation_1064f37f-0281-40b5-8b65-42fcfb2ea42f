import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Providers from "@/components/Providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Thông Minh Cho Người Việt",
    template: "%s | Infinite English"
  },
  description: "Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. <PERSON><PERSON> hợp mọi trình độ từ cơ bản đến nâng cao.",
  keywords: [
    "học tiếng anh",
    "học tiếng anh miễn phí",
    "học tiếng anh online",
    "AI học tiếng anh",
    "trắc nghiệm tiếng anh",
    "bài học tiếng anh",
    "từ vựng tiếng anh",
    "ngữ pháp tiếng anh",
    "luyện nói tiếng anh",
    "luyện thi TOEIC",
    "luyện thi IELTS",
    "phát âm tiếng anh",
    "giao tiếp tiếng anh",
    "khóa học tiếng anh miễn phí",
    "luyện tập tiếng anh",
    "tiếng anh cơ bản",
    "tiếng anh giao tiếp",
    "học tiếng anh cho người mới bắt đầu",
    "học tiếng anh cho người Việt",
    "ứng dụng học tiếng anh",
    "website học tiếng anh",
    "English learning",
    "learn English online",
    "free English course"
  ],
  authors: [{ name: "Infinite English Team" }],
  creator: "Infinite English",
  publisher: "Infinite English",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://inenglish.io.vn'),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'vi_VN',
    url: 'https://inenglish.io.vn',
    title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',
    description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',
    siteName: 'Infinite English',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'Infinite English - Nền Tảng Học Tiếng Anh với AI Cho Người Việt',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',
    description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',
    images: ['/logo.png'],
    creator: '@InfiniteEnglish',
  },
  alternates: {
    canonical: '/',
    languages: {
      'vi-VN': '/',
      'en-US': '/?lang=en',
      'zh-CN': '/?lang=zh',
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <head>
        <link rel="icon" type="image/svg+xml" href="/logo.svg" />
        <link rel="apple-touch-icon" href="/logo.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="google-site-verification" content="your-google-verification-code" />
        <link rel="canonical" href="inenglish.io.vn" />



        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Infinite English",
              "description": "Ứng dụng học tiếng Anh miễn phí với AI tạo câu hỏi và bài tập vô hạn cho người Việt",
              "url": "https://inenglish.io.vn",
              "logo": "https://inenglish.io.vn/logo.png",
              "applicationCategory": "EducationalApplication",
              "operatingSystem": "Web Browser",
              "browserRequirements": "Requires JavaScript. Requires HTML5.",
              "softwareVersion": "1.0",
              "author": {
                "@type": "Organization",
                "name": "Infinite English Team"
              },
              "publisher": {
                "@type": "Organization",
                "name": "Infinite English"
              },
              "inLanguage": ["vi-VN", "en-US"],
              "audience": {
                "@type": "EducationalAudience",
                "educationalRole": "student"
              },
              "educationalUse": "instruction",
              "learningResourceType": [
                "exercise",
                "quiz",
                "lesson",
                "practice"
              ],
              "educationalLevel": [
                "beginner",
                "intermediate",
                "advanced"
              ],
              "about": {
                "@type": "Thing",
                "name": "English Language Learning",
                "description": "Học tiếng Anh từ cơ bản đến nâng cao"
              },
              "featureList": [
                "Câu hỏi trắc nghiệm vô hạn được tạo bởi AI",
                "Bài học có cấu trúc theo trình độ",
                "Luyện tập từ vựng và ngữ pháp",
                "Hỗ trợ đa ngôn ngữ",
                "Hoàn toàn miễn phí"
              ],
              "isAccessibleForFree": true,
              "license": "https://creativecommons.org/licenses/by-nc-sa/4.0/"
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          {children}
        </Providers>
        {/* <SpacingDebug /> */}
      </body>
    </html>
  );
}
