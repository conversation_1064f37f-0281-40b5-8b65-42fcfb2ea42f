'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function Footer() {
  const { t } = useLanguage();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10">
                <img
                  src="/logo.svg"
                  alt="Infinite English"
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Infinite English</h3>
                <p className="text-sm text-gray-400">{t('appSubtitle')}</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed mb-6">
              {t('footerDescription')}
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2">
              <h4 className="font-semibold text-white mb-3">{t('contactAdvertising')}</h4>
              <p className="text-sm text-gray-300">
                Email: <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors"><EMAIL></a>
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">{t('quickLinks')}</h4>
            <ul className="space-y-2">
              <li>
                <a href="/about" className="text-gray-300 hover:text-white transition-colors text-sm">
                  {t('about')}
                </a>
              </li>
              <li>
                <a href="/contact" className="text-gray-300 hover:text-white transition-colors text-sm">
                  {t('contact')}
                </a>
              </li>
              <li>
                <a href="/lessons" className="text-gray-300 hover:text-white transition-colors text-sm">
                  AI Lessons
                </a>
              </li>
              <li>
                <a href="/reading" className="text-gray-300 hover:text-white transition-colors text-sm">
                  Reading
                </a>
              </li>
              <li>
                <a href="/tutor" className="text-gray-300 hover:text-white transition-colors text-sm">
                  AI Tutor
                </a>
              </li>
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">{t('legal')}</h4>
            <ul className="space-y-2">
              <li>
                <a href="/terms" className="text-gray-300 hover:text-white transition-colors text-sm">
                  {t('terms')}
                </a>
              </li>
              <li>
                <a href="/privacy" className="text-gray-300 hover:text-white transition-colors text-sm">
                  {t('privacy')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          {/* Copyright and Disclaimer */}
          <div className="text-center space-y-4">
            <p className="text-sm text-gray-400">
              Copyright © 2025 Infinite English. {t('allRightsReserved')}
            </p>
            
            {/* Disclaimer */}
            <div className="max-w-4xl mx-auto">
              <h5 className="font-semibold text-white mb-3">{t('disclaimer')}</h5>
              <p className="text-xs text-gray-400 leading-relaxed">
                {t('disclaimerText')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
