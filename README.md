# 🌍 Infinite English - Nền Tảng Học Tiếng Anh Miễn Phí với AI

Nền tảng học tiếng Anh hiện đại, tương tác được hỗ trợ bởi Google Gemini AI, với câu hỏi AI vô hạn, hỗ trợ đa ngôn ngữ và theo dõi tiến độ học tập.

## ✨ Tính Năng Nổi Bật

- 🤖 **Nội Dung AI**: Câu hỏi độc đáo vô hạn được tạo bởi Google Gemini AI
- 🌐 **Hỗ Trợ Đa Ngôn Ngữ**: Học bằng tiếng Việt, tiếng Trung hoặc tiếng Anh
- 📊 **Theo Dõi Tiến Độ**: <PERSON><PERSON><PERSON><PERSON> sát hành trình học tập với thống kê chi tiết
- 🔐 **Đăng Nhập Tùy Chọn**: Đăng nhập bằng Google để lưu tiến độ
- 🎨 **Giao Diện Hiện Đại**: Thiế<PERSON> kế glass morphism đẹp mắt với hiệu ứng mượt mà
- 📱 **Thiết Kế Responsive**: Ho<PERSON>t động hoàn hảo trên máy tính, tablet và điện thoại
- ⚡ **Server-Side Rendering**: Tải nhanh với Next.js SSR, tối ưu SEO cho người Việt

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- MongoDB database
- Google Cloud Console account (for OAuth and Gemini AI)

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd infinite-language
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**

   Create a `.env.local` file in the root directory:
   ```env
   # MongoDB Connection String
   MONGODB_URI=mongodb://localhost:27017/infinite_language_db

   # JWT Secret for authentication
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

   # NextAuth Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-nextauth-secret-key

   # Google OAuth
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret

   # Gemini AI (có thể dùng nhiều key cách nhau bằng dấu phẩy)
   GEMINI_API_KEY=your-gemini-api-key-1,your-gemini-api-key-2,your-gemini-api-key-3
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```
   ``
5. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

### Gemini AI Setup

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create multiple API keys (khuyến nghị 3-5 keys để tránh rate limit)
3. Add the keys to your `.env.local` file, separated by commas
4. Xem chi tiết trong [MULTIPLE_API_KEYS_SETUP.md](./MULTIPLE_API_KEYS_SETUP.md)

### MongoDB Setup

You can use either:
- Local MongoDB installation
- MongoDB Atlas (cloud)
- Docker container

## 🎯 Usage

### Learning Levels

- **🌱 Beginner**: Basic vocabulary and simple grammar
- **🌿 Intermediate**: Complex grammar and phrasal verbs
- **🌳 Advanced**: Advanced vocabulary, idioms, and complex structures

### Language Support

- **English**: Default interface language
- **Vietnamese**: Vietnamese explanations for Vietnamese learners
- **Chinese**: Chinese explanations for Chinese learners

### Features

1. **Quiz Flow**: Select level → Answer questions → Get instant feedback
2. **Progress Tracking**: Login to save your quiz history and statistics
3. **Multi-language**: Switch interface language anytime
4. **Responsive Design**: Use on any device

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS with custom animations
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: MongoDB with Mongoose
- **AI**: Google Gemini AI for question generation
- **Deployment**: Vercel-ready

## 📁 Project Structure

```
infinite-english/
├── src/
│   ├── app/                 # Next.js app router
│   │   ├── api/            # API routes
│   │   ├── quiz/           # Quiz pages
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   ├── contexts/          # React contexts
│   ├── lib/               # Utility libraries
│   ├── models/            # MongoDB models
│   └── types/             # TypeScript types
├── public/                # Static assets
└── package.json          # Dependencies
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Other Platforms

The app is compatible with any Node.js hosting platform that supports Next.js.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Google Gemini AI for question generation
- NextAuth.js for authentication
- Tailwind CSS for styling
- MongoDB for data storage

---

Made with ❤️ for language learners worldwide 🌍
