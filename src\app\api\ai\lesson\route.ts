import { NextRequest, NextResponse } from 'next/server';
import { generateLessonWithGemini } from '@/lib/gemini';
import { Level, Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { 
      level, 
      language = 'en', 
      topic,
      lessonType = 'grammar',
      duration = 30
    } = await request.json();

    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    if (!topic || topic.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Topic is required' },
        { status: 400 }
      );
    }

    const validTypes = ['grammar', 'vocabulary', 'conversation', 'pronunciation', 'writing', 'listening', 'culture'];
    if (!validTypes.includes(lessonType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid lesson type provided' },
        { status: 400 }
      );
    }

    if (duration < 10 || duration > 120) {
      return NextResponse.json(
        { success: false, error: 'Duration must be between 10 and 120 minutes' },
        { status: 400 }
      );
    }

    // Generate lesson using Gemini AI
    const lesson = await generateLessonWithGemini({
      level,
      language,
      topic,
      lessonType,
      duration
    });

    return NextResponse.json({
      success: true,
      lesson
    });
  } catch (error) {
    console.error('Error generating lesson:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate lesson' },
      { status: 500 }
    );
  }
}
