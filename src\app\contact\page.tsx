'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function ContactPage() {
  const { t, language } = useLanguage();

  const getContent = () => {
    switch (language) {
      case 'vi':
        return {
          title: "Liên hệ với chúng tôi",
          subtitle: "Chúng tôi luôn sẵn sàng hỗ trợ bạn trong hành trình học tiếng Anh",
          sections: [
            {
              title: "Thông tin liên hệ",
              items: [
                { label: "Email liên hệ", value: "<EMAIL>" }
              ]
            }
          ],
          faqTitle: "Câu hỏi thường gặp",
          faqs: [
            {
              question: "Infinite English có miễn phí không?",
              answer: "Có, Infinite English hoàn toàn miễn phí. Chúng tôi cam kết cung cấp giáo dục chất lượng cao mà không tính phí."
            },
            {
              question: "Tôi có cần tạo tài khoản không?",
              answer: "Bạn có thể sử dụng các tính năng cơ bản mà không cần tài khoản. Tuy nhiên, việc tạo tài khoản sẽ giúp bạn theo dõi tiến độ học tập."
            },
            {
              question: "Làm thế nào để báo cáo lỗi?",
              answer: "Vui lòng gửi email chi tiết về lỗi đến <EMAIL>. Chúng tôi sẽ xử lý trong vòng 24 giờ."
            },
            {
              question: "Tôi có thể đề xuất tính năng mới không?",
              answer: "Chúng tôi luôn hoan nghênh ý kiến đóng góp! Hãy gửi đề xuất của bạn đến <EMAIL>."
            }
          ]
        };
      case 'zh':
        return {
          title: "联系我们",
          subtitle: "我们随时准备在您的英语学习之旅中为您提供支持",
          sections: [
            {
              title: "联系信息",
              items: [
                { label: "联系邮箱", value: "<EMAIL>" }
              ]
            }
          ],
          faqTitle: "常见问题",
          faqs: [
            {
              question: "Infinite English 是免费的吗？",
              answer: "是的，Infinite English 完全免费。我们致力于提供高质量的教育而不收取费用。"
            },
            {
              question: "我需要创建账户吗？",
              answer: "您可以在不创建账户的情况下使用基本功能。但是，创建账户将帮助您跟踪学习进度。"
            },
            {
              question: "如何报告错误？",
              answer: "请将错误详情发送至 <EMAIL>。我们将在24小时内处理。"
            },
            {
              question: "我可以建议新功能吗？",
              answer: "我们总是欢迎反馈！请将您的建议发送至 <EMAIL>。"
            }
          ]
        };
      default:
        return {
          title: "Contact Us",
          subtitle: "We're here to support you on your English learning journey",
          sections: [
            {
              title: "Contact Information",
              items: [
                { label: "Contact Email", value: "<EMAIL>" }
              ]
            }
          ],
          faqTitle: "Frequently Asked Questions",
          faqs: [
            {
              question: "Is Infinite English free?",
              answer: "Yes, Infinite English is completely free. We're committed to providing quality education at no cost."
            },
            {
              question: "Do I need to create an account?",
              answer: "You can use basic features without an account. However, creating an account helps you track your learning progress."
            },
            {
              question: "How do I report a bug?",
              answer: "Please email detailed information about the <NAME_EMAIL>. We'll address it within 24 hours."
            },
            {
              question: "Can I suggest new features?",
              answer: "We always welcome feedback! Please send your <NAME_EMAIL>."
            }
          ]
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {content.title}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {content.subtitle}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Contact Information */}
            <div className="space-y-8">
              {content.sections.map((section, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    {section.title}
                  </h2>
                  <div className="space-y-4">
                    {section.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex justify-between items-center">
                        <span className="text-gray-600">{item.label}:</span>
                        <span className="font-medium text-gray-900">{item.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {/* FAQ Section */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  {content.faqTitle}
                </h2>
                <div className="space-y-6">
                  {content.faqs.map((faq, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {faq.question}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
