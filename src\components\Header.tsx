'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';
import UserMenu from './UserMenu';

interface HeaderProps {
  onAuthClick?: () => void;
  onHistoryClick?: () => void;
  showBackButton?: boolean;
  onBackClick?: () => void;
  showScore?: boolean;
  score?: { correct: number; total: number };
  onResetScore?: () => void;
}

export default function Header({
  onAuthClick,
  onHistoryClick,
  showBackButton = false,
  onBackClick,
  showScore = false,
  score,
  onResetScore
}: HeaderProps) {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg shadow-black/5'
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 header-container">
        <div className="flex items-center justify-between h-16 gap-2">
          {/* Left side */}
          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            {showBackButton && onBackClick && (
              <button
                onClick={onBackClick}
                className={`group flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-500 shadow-sm hover:shadow-md ${
                  isScrolled
                    ? 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900'
                    : 'bg-white/80 hover:bg-white/90 text-gray-700 hover:text-gray-900 backdrop-blur-sm'
                }`}
              >
                <svg className="w-4 h-4 transition-transform group-hover:-translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="text-sm font-semibold">{t('backToLevels')}</span>
              </button>
            )}

            {/* Logo/Title */}
            <div className="flex items-center space-x-2 min-w-0 flex-1 sm:flex-initial">
              <div className="w-8 h-8 relative flex-shrink-0">
                <img
                  src="/logo.svg"
                  alt="Infinite English - Học Tiếng Anh với AI"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="hidden sm:block min-w-0">
                <h1 className={`text-xl font-bold transition-all duration-500 ${
                  isScrolled
                    ? 'text-blue-700'
                    : 'text-blue-800 drop-shadow-lg'
                }`}>
                  Infinite English
                </h1>
                <p className={`text-xs font-medium transition-all duration-500 max-w-xs truncate ${
                  isScrolled ? 'text-gray-500' : 'text-gray-600 drop-shadow-md'
                }`}>{t('appSubtitle')}</p>
              </div>
              <div className="sm:hidden min-w-0 flex-1">
                <h1 className={`text-lg font-bold transition-all duration-500 truncate ${
                  isScrolled
                    ? 'text-blue-700'
                    : 'text-blue-800 drop-shadow-lg'
                }`}>
                  Infinite English
                </h1>
              </div>
            </div>

            {/* Navigation Links */}
            <nav className="hidden md:flex items-center space-x-1 ml-8">
              <a
                href="/"
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-white/50'
                }`}
              >
                Quiz
              </a>
              <a
                href="/lessons"
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-white/50'
                }`}
              >
                AI Lessons
              </a>
              <a
                href="/reading"
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-white/50'
                }`}
              >
                Reading
              </a>
              <a
                href="/tutor"
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-white/50'
                }`}
              >
                AI Tutor
              </a>
            </nav>
          </div>

          {/* Center - Score (if shown) */}
          {showScore && score && (
            <div className="hidden sm:flex items-center space-x-3 flex-shrink-0">
              <div className={`flex items-center space-x-2 px-3 py-2 rounded-xl shadow-sm transition-all duration-500 ${
                isScrolled
                  ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700'
                  : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'
              }`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500 ${
                  isScrolled ? 'bg-emerald-100' : 'bg-emerald-100'
                }`}>
                  <svg className={`w-3 h-3 transition-colors duration-500 ${
                    isScrolled ? 'text-emerald-600' : 'text-emerald-600'
                  }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <span className="text-xs font-bold">
                    {t('score')}: {score.correct}/{score.total}
                  </span>
                  <div className={`text-xs transition-colors duration-500 ${
                    isScrolled ? 'text-emerald-600' : 'text-emerald-600'
                  }`}>
                    {score.total > 0 ? Math.round((score.correct / score.total) * 100) : 0}%
                  </div>
                </div>
              </div>

              {/* Reset Score Button */}
              {onResetScore && score.total > 0 && (
                <button
                  onClick={onResetScore}
                  className={`group p-2 rounded-lg transition-all duration-500 shadow-sm hover:shadow-md ${
                    isScrolled
                      ? 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'
                      : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'
                  }`}
                  title={t('resetScore') || 'Reset Score'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              )}
            </div>
          )}

          {/* Mobile Score - shown on small screens */}
          {showScore && score && (
            <div className="sm:hidden flex items-center">
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-bold transition-all duration-500 ${
                isScrolled
                  ? 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700'
                  : 'bg-gradient-to-r from-emerald-50 to-blue-50 text-emerald-700 backdrop-blur-sm'
              }`}>
                <span>{score.correct}/{score.total}</span>
              </div>
            </div>
          )}

          {/* Right side */}
          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            {/* Language Switcher */}
            <LanguageSwitcher isScrolled={isScrolled} />

            {/* Auth/User Menu */}
            {user ? (
              onHistoryClick && <UserMenu onHistoryClick={onHistoryClick} />
            ) : (
              onAuthClick && (
                <button
                  onClick={onAuthClick}
                  className={`group relative px-6 py-2.5 rounded-xl transition-all duration-500 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${
                    isScrolled
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white'
                      : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white backdrop-blur-sm'
                  }`}
                >
                  <span className="relative z-10">{t('login')}</span>
                  {isScrolled && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl"></div>
                  )}
                </button>
              )
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
