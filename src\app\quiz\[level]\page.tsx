import { notFound } from 'next/navigation';
import Link from 'next/link';
import { generateQuestionWithGemini } from '@/lib/gemini';
import { Level, Language, Question } from '@/types';
import { questionStore } from '@/lib/questionStore';
import QuizClient from '@/components/QuizClient';

interface QuizPageProps {
  params: Promise<{
    level: string;
  }>;
  searchParams: Promise<{
    lang?: string;
  }>;
}

// Validate level parameter
function isValidLevel(level: string): level is Level {
  return ['beginner', 'intermediate', 'advanced'].includes(level);
}

// Validate language parameter
function isValidLanguage(lang: string): lang is Language {
  return ['en', 'vi', 'zh'].includes(lang);
}

export default async function QuizPage({ params, searchParams }: QuizPageProps) {
  const { level } = await params;
  const resolvedSearchParams = await searchParams;
  const language = resolvedSearchParams.lang || 'vi';

  // Validate parameters
  if (!isValidLevel(level)) {
    notFound();
  }

  if (!isValidLanguage(language)) {
    notFound();
  }

  try {
    // Generate question server-side using Gemini AI
    const fullQuestion = await generateQuestionWithGemini(level, language);

    // Store the answer and explanation server-side
    await questionStore.set(fullQuestion.id, {
      correctAnswer: fullQuestion.correctAnswer,
      explanation: fullQuestion.explanation,
      level: fullQuestion.level,
      language: language
    });

    // Return only question data without answer for client
    const safeQuestion: Question = {
      id: fullQuestion.id,
      question: fullQuestion.question,
      options: fullQuestion.options,
      level: fullQuestion.level
    };

    return (
      <QuizClient
        initialQuestion={safeQuestion}
        level={level}
        language={language}
      />
    );
  } catch (error) {
    console.error('Error generating question:', error);
    
    // Return error page or fallback
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              Failed to Generate Question
            </h2>
            <p className="text-gray-600 mb-4">
              Sorry, we couldn&apos;t generate a question at this time. Please try again later.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: QuizPageProps) {
  const { level } = await params;
  
  if (!isValidLevel(level)) {
    return {
      title: 'Quiz Not Found',
    };
  }

  return {
    title: `${level.charAt(0).toUpperCase() + level.slice(1)} English Quiz`,
    description: `Test your English skills with our ${level} level quiz. Improve your English learning with AI-generated questions.`,
  };
}

// Generate static params for better performance (optional)
export async function generateStaticParams() {
  return [
    { level: 'beginner' },
    { level: 'intermediate' },
    { level: 'advanced' },
  ];
}
