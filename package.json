{"name": "infinite-english", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed.js", "seed:reset": "curl -X DELETE http://localhost:3000/api/seed", "test:redis": "node scripts/test-redis.js"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@google/generative-ai": "^0.24.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "next": "15.3.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "redis": "^5.5.6", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}