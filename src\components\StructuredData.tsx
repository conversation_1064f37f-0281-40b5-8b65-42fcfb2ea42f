interface StructuredDataProps {
  type: 'website' | 'course' | 'article' | 'educational-organization';
  data: any;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
    };

    switch (type) {
      case 'website':
        return {
          ...baseData,
          "@type": "WebSite",
          "name": "Infinite English",
          "description": "Học tiếng Anh với AI - Nền tảng học tiếng Anh miễn phí dành cho người Việt với bài học, trắc nghiệm và tài liệu học tập toàn diện",
          "url": "https://inenglish.io.vn",
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": "https://inenglish.io.vn/search?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Infinite English",
            "logo": {
              "@type": "ImageObject",
              "url": "https://inenglish.io.vn/logo.png"
            }
          }
        };

      case 'educational-organization':
        return {
          ...baseData,
          "@type": "EducationalOrganization",
          "name": "Infinite English",
          "description": "Nền tảng học tiếng Anh với AI dành cho người Việt - Bài học toàn diện và tài liệu luyện tập miễn phí",
          "url": "https://inenglish.io.vn",
          "logo": "https://inenglish.io.vn/logo.png",
          "sameAs": [],
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "VN"
          },
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND",
            "availability": "https://schema.org/InStock"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "English Learning Courses",
            "itemListElement": [
              {
                "@type": "Course",
                "name": "Beginner English",
                "description": "Basic vocabulary and simple grammar for English beginners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Beginner",
                "courseMode": "Online",
                "isAccessibleForFree": true
              },
              {
                "@type": "Course", 
                "name": "Intermediate English",
                "description": "More complex sentences and vocabulary for intermediate learners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Intermediate",
                "courseMode": "Online",
                "isAccessibleForFree": true
              },
              {
                "@type": "Course",
                "name": "Advanced English",
                "description": "Complex grammar and advanced vocabulary for advanced learners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Advanced",
                "courseMode": "Online",
                "isAccessibleForFree": true
              }
            ]
          }
        };

      case 'course':
        return {
          ...baseData,
          "@type": "Course",
          "name": data.name,
          "description": data.description,
          "provider": {
            "@type": "Organization",
            "name": "Infinite English",
            "url": "https://inenglish.io.vn"
          },
          "educationalLevel": data.level,
          "courseMode": "Online",
          "isAccessibleForFree": true,
          "inLanguage": "vi",
          "availableLanguage": ["vi", "en", "zh"],
          "teaches": data.objectives || [],
          "timeRequired": data.duration ? `PT${data.duration}M` : undefined,
          "coursePrerequisites": data.prerequisites || [],
          "educationalCredentialAwarded": "Certificate of Completion"
        };

      case 'article':
        return {
          ...baseData,
          "@type": "Article",
          "headline": data.title,
          "description": data.description,
          "author": {
            "@type": "Person",
            "name": data.author
          },
          "publisher": {
            "@type": "Organization",
            "name": "Infinite English",
            "logo": {
              "@type": "ImageObject",
              "url": "https://inenglish.io.vn/logo.png"
            }
          },
          "datePublished": data.publishDate,
          "dateModified": data.modifiedDate || data.publishDate,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data.url
          },
          "articleSection": data.category,
          "keywords": data.tags?.join(", "),
          "wordCount": data.wordCount,
          "timeRequired": data.readTime ? `PT${data.readTime}M` : undefined,
          "inLanguage": "vi",
          "isAccessibleForFree": true
        };

      default:
        return baseData;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData())
      }}
    />
  );
}
