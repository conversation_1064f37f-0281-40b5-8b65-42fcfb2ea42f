import GoogleProvider from "next-auth/providers/google";
// import FacebookProvider from "next-auth/providers/facebook";
import connectDB from "./mongodb";
import User from "@/models/User";

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // FacebookProvider({
    //   clientId: process.env.FACEBOOK_CLIENT_ID!,
    //   clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    // }),
  ],
  session: {
    strategy: "jwt" as const,
  },
  callbacks: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async jwt({ token, user, account }: any) {
      if (user && account) {
        // For social login, create/update user in our database
        try {
          await connectDB();
          const existingUser = await User.findOne({ email: user.email });

          if (!existingUser) {
            // Create new user for social login
            const newUser = new User({
              email: user.email,
              username: user.name || user.email?.split('@')[0],
              provider: account.provider,
              stats: {
                totalQuestions: 0,
                correctAnswers: 0,
                streakCount: 0,
                lastActiveDate: new Date(),
                levelStats: {
                  beginner: { total: 0, correct: 0 },
                  intermediate: { total: 0, correct: 0 },
                  advanced: { total: 0, correct: 0 },
                }
              },
              preferredLanguage: 'en'
            });

            const savedUser = await newUser.save();
            token.userId = savedUser._id.toString();
            token.username = savedUser.username;
          } else {
            // Update existing user's last active date
            existingUser.stats.lastActiveDate = new Date();
            await existingUser.save();
            token.userId = existingUser._id.toString();
            token.username = existingUser.username;
          }
        } catch (error) {
          console.error("Error creating/updating user:", error);
        }
      }
      return token;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.userId as string;
        session.user.username = token.username as string;
      }
      return session;
    }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  secret: process.env.NEXTAUTH_SECRET,
};
