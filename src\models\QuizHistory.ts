import mongoose, { Document, Schema, Types } from 'mongoose';
import { Language, Level } from '@/types';

export interface IQuizHistory extends Document {
  _id: string;
  userId: Types.ObjectId;
  questionId: string;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer: number;
  isCorrect: boolean;
  explanation: string;
  level: Level;
  language: Language;
  timeSpent: number; // in seconds
  createdAt: Date;
}

const QuizHistorySchema = new Schema<IQuizHistory>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  questionId: {
    type: String,
    required: true,
  },
  question: {
    type: String,
    required: true,
  },
  options: [{
    type: String,
    required: true,
  }],
  correctAnswer: {
    type: Number,
    required: true,
  },
  userAnswer: {
    type: Number,
    required: true,
  },
  isCorrect: {
    type: Boolean,
    required: true,
  },
  explanation: {
    type: String,
    required: true,
  },
  level: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true,
  },
  language: {
    type: String,
    enum: ['en', 'vi', 'zh'],
    required: true,
  },
  timeSpent: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Index for efficient queries
QuizHistorySchema.index({ userId: 1, createdAt: -1 });
QuizHistorySchema.index({ userId: 1, level: 1 });
QuizHistorySchema.index({ userId: 1, isCorrect: 1 });

export default mongoose.models.QuizHistory || mongoose.model<IQuizHistory>('QuizHistory', QuizHistorySchema);
