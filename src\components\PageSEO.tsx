import Head from 'next/head';
import { useLanguage } from '@/contexts/LanguageContext';
import StructuredData from './StructuredData';

interface PageSEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  structuredDataType?: 'website' | 'quiz' | 'course' | 'article' | 'educational-organization';
  structuredData?: any;
}

export default function PageSEO({
  title,
  description,
  keywords,
  image = '/logo.svg',
  url = 'https://inenglish.io.vn',
  type = 'website',
  structuredDataType = 'website',
  structuredData = {}
}: PageSEOProps) {
  const { language } = useLanguage();

  const seoData = {
    vi: {
      quiz: {
        title: 'Trắc Nghiệm Tiếng Anh Miễn Phí | Infinite English',
        description: '<PERSON>yện tập trắc nghiệm tiếng Anh với AI thông minh. Câu hỏi vô hạn theo từng cấp độ, kiểm tra trình độ và cải thiện kỹ năng tiếng Anh hiệu quả.',
        keywords: 'trắc nghiệm tiếng anh, kiểm tra tiếng anh, quiz tiếng anh, luyện thi tiếng anh, đề thi tiếng anh, bài tập tiếng anh'
      },
      lessons: {
        title: 'Bài Học Tiếng Anh Miễn Phí | Infinite English',
        description: 'Học tiếng Anh qua các bài học có cấu trúc từ cơ bản đến nâng cao. Nội dung được tạo bởi AI, phù hợp với người Việt học tiếng Anh.',
        keywords: 'bài học tiếng anh, học tiếng anh cơ bản, học tiếng anh nâng cao, khóa học tiếng anh, giáo trình tiếng anh'
      },
      reading: {
        title: 'Luyện Đọc Hiểu Tiếng Anh | Infinite English',
        description: 'Cải thiện kỹ năng đọc hiểu tiếng Anh với các bài đọc đa dạng chủ đề. Từ cơ bản đến nâng cao, phù hợp mọi trình độ.',
        keywords: 'đọc hiểu tiếng anh, luyện đọc tiếng anh, reading comprehension, bài đọc tiếng anh, kỹ năng đọc'
      },
      tutor: {
        title: 'AI Gia Sư Tiếng Anh | Infinite English',
        description: 'Học tiếng Anh 1-1 với AI gia sư thông minh. Giải đáp thắc mắc, hướng dẫn ngữ pháp và cải thiện kỹ năng giao tiếp tiếng Anh.',
        keywords: 'gia sư tiếng anh, AI tutor, học tiếng anh 1-1, hỏi đáp tiếng anh, gia sư AI'
      }
    },
    en: {
      quiz: {
        title: 'Free English Quiz | Infinite English',
        description: 'Practice English with AI-powered quizzes. Unlimited questions by level, test your proficiency and improve English skills effectively.',
        keywords: 'english quiz, english test, english practice, english assessment, language quiz'
      },
      lessons: {
        title: 'Free English Lessons | Infinite English',
        description: 'Learn English through structured lessons from basic to advanced. AI-generated content designed for Vietnamese learners.',
        keywords: 'english lessons, learn english, english course, english tutorial, english study'
      },
      reading: {
        title: 'English Reading Comprehension | Infinite English',
        description: 'Improve English reading skills with diverse topics. From basic to advanced level, suitable for all proficiency levels.',
        keywords: 'english reading, reading comprehension, english texts, reading practice, reading skills'
      },
      tutor: {
        title: 'AI English Tutor | Infinite English',
        description: 'Learn English 1-on-1 with intelligent AI tutor. Get answers, grammar guidance and improve English communication skills.',
        keywords: 'english tutor, AI tutor, english learning, english Q&A, personal tutor'
      }
    },
    zh: {
      quiz: {
        title: '免费英语测验 | Infinite English',
        description: '通过AI驱动的测验练习英语。按级别提供无限题目，测试水平并有效提高英语技能。',
        keywords: '英语测验, 英语考试, 英语练习, 英语评估, 语言测验'
      },
      lessons: {
        title: '免费英语课程 | Infinite English',
        description: '通过从基础到高级的结构化课程学习英语。为越南学习者设计的AI生成内容。',
        keywords: '英语课程, 学英语, 英语教程, 英语学习, 英语研究'
      },
      reading: {
        title: '英语阅读理解 | Infinite English',
        description: '通过多样化主题提高英语阅读技能。从基础到高级水平，适合所有熟练程度。',
        keywords: '英语阅读, 阅读理解, 英语文本, 阅读练习, 阅读技能'
      },
      tutor: {
        title: 'AI英语导师 | Infinite English',
        description: '与智能AI导师一对一学习英语。获得答案、语法指导并提高英语交流技能。',
        keywords: '英语导师, AI导师, 英语学习, 英语问答, 个人导师'
      }
    }
  };

  // Determine page type from URL or title
  let pageType = 'website';
  if (url.includes('/quiz') || title?.includes('Quiz') || title?.includes('Trắc Nghiệm')) {
    pageType = 'quiz';
  } else if (url.includes('/lessons') || title?.includes('Lesson') || title?.includes('Bài Học')) {
    pageType = 'lessons';
  } else if (url.includes('/reading') || title?.includes('Reading') || title?.includes('Đọc')) {
    pageType = 'reading';
  } else if (url.includes('/tutor') || title?.includes('Tutor') || title?.includes('Gia Sư')) {
    pageType = 'tutor';
  }

  const currentSEO = seoData[language]?.[pageType as keyof typeof seoData.vi] || seoData.vi.quiz;
  const finalTitle = title || currentSEO.title;
  const finalDescription = description || currentSEO.description;
  const finalKeywords = keywords || currentSEO.keywords;

  return (
    <>
      <Head>
        <title>{finalTitle}</title>
        <meta name="description" content={finalDescription} />
        <meta name="keywords" content={finalKeywords} />
        
        {/* Open Graph */}
        <meta property="og:title" content={finalTitle} />
        <meta property="og:description" content={finalDescription} />
        <meta property="og:image" content={image} />
        <meta property="og:url" content={url} />
        <meta property="og:type" content={type} />
        <meta property="og:locale" content={language === 'vi' ? 'vi_VN' : language === 'zh' ? 'zh_CN' : 'en_US'} />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={finalTitle} />
        <meta name="twitter:description" content={finalDescription} />
        <meta name="twitter:image" content={image} />
        
        {/* Additional SEO */}
        <meta name="robots" content="index, follow" />
        <meta name="author" content="Infinite English" />
        <meta name="monetization" content="advertising" />
        <meta name="ads-supported" content="true" />
        <meta name="content-rating" content="general" />
        <meta name="target-audience" content="vietnamese learners" />
        <link rel="canonical" href={url} />

        {/* Language alternatives */}
        <link rel="alternate" hrefLang="vi" href={url.replace(/\?.*/, '') + '?lang=vi'} />
        <link rel="alternate" hrefLang="en" href={url.replace(/\?.*/, '') + '?lang=en'} />
        <link rel="alternate" hrefLang="zh" href={url.replace(/\?.*/, '') + '?lang=zh'} />
        <link rel="alternate" hrefLang="x-default" href={url.replace(/\?.*/, '')} />
        
        {/* AdSense optimization */}
        <meta name="google-adsense-account" content={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID} />
        <meta name="google-adsense-platform-account" content={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID} />
      </Head>
      
      <StructuredData 
        type={structuredDataType} 
        data={{
          title: finalTitle,
          description: finalDescription,
          url: url,
          level: pageType === 'quiz' ? 'beginner' : undefined,
          ...structuredData
        }} 
      />
    </>
  );
}
