'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { User, AuthContextType } from '@/types';
import type { Session } from 'next-auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const loading = status === 'loading';

  const checkAuth = useCallback(async () => {
    const customSession = session as Session | null;
    if (customSession?.user) {
      try {
        const response = await fetch('/api/auth/me');
        const data = await response.json();

        if (data.success) {
          setUser(data.user);
        } else {
          // If API fails, create user object from session
          setUser({
            id: customSession.user.id || '',
            email: customSession.user.email || '',
            username: customSession.user.username || customSession.user.name || '',
            preferredLanguage: 'en',
            stats: {
              totalQuestions: 0,
              correctAnswers: 0,
              streakCount: 0,
              lastActiveDate: new Date(),
              levelStats: {
                beginner: { total: 0, correct: 0 },
                intermediate: { total: 0, correct: 0 },
                advanced: { total: 0, correct: 0 },
              }
            }
          });
        }
      } catch (error) {
        console.error('Auth check error:', error);
        // Fallback to session data
        setUser({
          id: customSession.user.id || '',
          email: customSession.user.email || '',
          username: customSession.user.username || customSession.user.name || '',
          preferredLanguage: 'en',
          stats: {
            totalQuestions: 0,
            correctAnswers: 0,
            streakCount: 0,
            lastActiveDate: new Date(),
            levelStats: {
              beginner: { total: 0, correct: 0 },
              intermediate: { total: 0, correct: 0 },
              advanced: { total: 0, correct: 0 },
            }
          }
        });
      }
    } else {
      setUser(null);
    }
  }, [session]);

  const logout = async (): Promise<void> => {
    try {
      await signOut({ redirect: false });
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null);
    }
  };

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const value: AuthContextType = {
    user,
    logout,
    loading,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
