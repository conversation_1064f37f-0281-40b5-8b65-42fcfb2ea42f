import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { Language } from '@/types';

export interface IUser extends Document {
  _id: string;
  email: string;
  username: string;
  password?: string; // Optional for social login users
  provider?: string; // Track login provider (google, facebook)
  preferredLanguage: Language;
  stats: {
    totalQuestions: number;
    correctAnswers: number;
    streakCount: number;
    lastActiveDate: Date;
    quizzesTaken?: number;
    averageAccuracy?: number;
    lastQuizDate?: Date;
    totalStudyTime?: number; // in minutes
    lessonsCompleted?: number;
    vocabularyLearned?: number;
    levelStats: {
      beginner: { total: number; correct: number };
      intermediate: { total: number; correct: number };
      advanced: { total: number; correct: number };
    };
  };
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 20,
  },
  password: {
    type: String,
    required: false, // Not required for social login users
    minlength: 6,
  },
  provider: {
    type: String,
    enum: ['google', 'facebook', 'credentials'],
    default: 'credentials',
  },
  preferredLanguage: {
    type: String,
    enum: ['en', 'vi', 'zh'],
    default: 'vi',
  },
  stats: {
    totalQuestions: { type: Number, default: 0 },
    correctAnswers: { type: Number, default: 0 },
    streakCount: { type: Number, default: 0 },
    lastActiveDate: { type: Date, default: Date.now },
    quizzesTaken: { type: Number, default: 0 },
    averageAccuracy: { type: Number, default: 0 },
    lastQuizDate: { type: Date },
    totalStudyTime: { type: Number, default: 0 }, // in minutes
    lessonsCompleted: { type: Number, default: 0 },
    vocabularyLearned: { type: Number, default: 0 },
    levelStats: {
      beginner: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 },
      },
      intermediate: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 },
      },
      advanced: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 },
      },
    },
  },
}, {
  timestamps: true,
});

// Hash password before saving
UserSchema.pre('save', async function () {
  if (!this.isModified('password') || !this.password) return;

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
  } catch (error: unknown) {
    throw error;
  }
});

// Compare password method (only for users with password)
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  if (!this.password) {
    return false; // Social login users don't have passwords
  }
  return bcrypt.compare(candidatePassword, this.password);
};

// Prevent password from being returned in JSON
UserSchema.methods.toJSON = function () {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
