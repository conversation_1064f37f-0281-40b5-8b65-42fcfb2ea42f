@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content p {
  margin-bottom: 0.5rem;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 0.5rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content li:last-child {
  margin-bottom: 0;
}

.markdown-content code {
  font-size: 0.875rem;
}

.markdown-content pre {
  margin-bottom: 0.5rem;
}

.markdown-content blockquote {
  margin-bottom: 0.5rem;
}

@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation-name: fade-in;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top-2;
}

.slide-in-from-bottom-4 {
  animation-name: slide-in-from-bottom-4;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Header spacing utility - ensures consistent spacing from fixed header */
.header-spacing {
  padding-top: 6rem; /* 96px - matches pt-24 */
}

/* Debug utility to visualize header spacing */
.debug-header-spacing {
  position: relative;
}

.debug-header-spacing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rem; /* Header height */
  background: rgba(255, 0, 0, 0.1);
  border-bottom: 2px solid red;
  z-index: 9999;
  pointer-events: none;
}

/* Quiz layout fixes */
.quiz-container {
  min-height: calc(100vh - 6rem); /* Account for header height + spacing */
}

.quiz-content {
  max-height: calc(100vh - 8rem); /* Account for header + padding */
}

/* Prevent header overflow on mobile */
.header-container {
  overflow: hidden;
}

.header-container > * {
  min-width: 0; /* Allow flex items to shrink */
}
