import { Metadata } from 'next';
import ReadingClient from '@/components/ReadingClientNew';

export const metadata: Metadata = {
  title: 'Reading Practice - Infinite English',
  description: 'Improve your English reading comprehension with engaging passages, vocabulary highlights, and comprehension questions for all levels.',
  keywords: 'English reading, reading comprehension, reading practice, English passages, vocabulary, comprehension questions',
  openGraph: {
    title: 'Reading Practice - Infinite English',
    description: 'Improve your English reading comprehension with engaging passages and comprehension questions.',
    type: 'website',
  },
};

export default function ReadingPage() {
  return <ReadingClient />;
}
