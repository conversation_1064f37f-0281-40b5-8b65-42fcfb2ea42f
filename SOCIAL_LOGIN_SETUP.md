# Social Login Setup Guide

This application now uses **only social login** (Google and Facebook) for authentication. Regular email/password login has been removed for simplicity and better user experience.

## Required Setup

### 1. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (or Google Identity API)
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Configure the OAuth consent screen
6. Add authorized redirect URIs:
   - For development: `http://localhost:3000/api/auth/callback/google`
   - For production: `https://yourdomain.com/api/auth/callback/google`
7. Copy the Client ID and Client Secret to your `.env.local` file

### 2. Facebook OAuth Setup

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or select an existing one
3. Add "Facebook Login" product to your app
4. In Facebook Login settings, add valid OAuth redirect URIs:
   - For development: `http://localhost:3000/api/auth/callback/facebook`
   - For production: `https://yourdomain.com/api/auth/callback/facebook`
5. Copy the App ID and App Secret to your `.env.local` file

### 3. Environment Variables

Copy `.env.example` to `.env.local` and fill in your credentials:

```bash
cp .env.example .env.local
```

Required variables:
- `GOOGLE_CLIENT_ID` - From Google Cloud Console
- `GOOGLE_CLIENT_SECRET` - From Google Cloud Console
- `FACEBOOK_CLIENT_ID` - From Facebook Developers
- `FACEBOOK_CLIENT_SECRET` - From Facebook Developers
- `NEXTAUTH_SECRET` - A random secret key for NextAuth
- `NEXTAUTH_URL` - Your app URL (http://localhost:3000 for development)

## Features

- ✅ **Social Login Only**: Simplified authentication with Google and Facebook
- ✅ **Improved UI**: Clean, professional modal design with better styling
- ✅ **Optional Login**: Users can use the app without logging in, but progress won't be saved
- ✅ **Automatic User Creation**: Users are automatically created in MongoDB when they sign in with social providers
- ✅ **Session Management**: Uses NextAuth.js for secure session handling

## Changes Made

1. **Removed Credentials Provider**: No more email/password authentication
2. **Updated AuthModal**: Clean social login interface only
3. **Improved Styling**: Better modal design with backdrop blur and professional appearance
4. **Updated User Model**: Password field is now optional for social login users
5. **Simplified AuthContext**: Uses NextAuth session directly
6. **Removed Unused APIs**: Deleted login/register API routes

## Testing

1. Start the development server: `npm run dev`
2. Open http://localhost:3000
3. Click the login button to see the new social login modal
4. Test Google and Facebook login (requires proper OAuth setup)

## Production Deployment

1. Update `NEXTAUTH_URL` to your production domain
2. Add production redirect URIs to Google and Facebook OAuth settings
3. Use secure, random values for `NEXTAUTH_SECRET`
4. Ensure MongoDB connection is properly configured for production
