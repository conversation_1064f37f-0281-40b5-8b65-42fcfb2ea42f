const { createClient } = require('redis');

async function testRedis() {
  console.log('🔄 Testing Redis connection...');
  
  const redis = createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    socket: {
      connectTimeout: 5000,
    },
  });

  redis.on('error', (err) => {
    console.error('❌ Redis Client Error:', err);
  });

  try {
    // Connect to Redis
    await redis.connect();
    console.log('✅ Connected to Redis successfully');

    // Test basic operations
    console.log('\n🧪 Testing basic operations...');
    
    // Set a test key
    await redis.set('test:key', 'Hello Redis!');
    console.log('✅ Set test key');

    // Get the test key
    const value = await redis.get('test:key');
    console.log('✅ Retrieved test key:', value);

    // Set with TTL
    await redis.setEx('test:ttl', 5, 'This will expire in 5 seconds');
    console.log('✅ Set key with 5 second TTL');

    // Check TTL
    const ttl = await redis.ttl('test:ttl');
    console.log('✅ TTL remaining:', ttl, 'seconds');

    // Test question storage format
    console.log('\n🧪 Testing question storage format...');
    
    const questionData = {
      correctAnswer: 2,
      explanation: 'This is a test explanation',
      level: 'beginner',
      language: 'en',
      createdAt: Date.now()
    };

    await redis.setEx('question:test-123', 3600, JSON.stringify(questionData));
    console.log('✅ Stored test question');

    const retrievedQuestion = await redis.get('question:test-123');
    const parsedQuestion = JSON.parse(retrievedQuestion);
    console.log('✅ Retrieved test question:', parsedQuestion);

    // Test one-time use (get and delete)
    await redis.setEx('question:one-time-789', 3600, JSON.stringify(questionData));
    console.log('✅ Created one-time use question');

    const oneTimeQuestion = await redis.get('question:one-time-789');
    if (oneTimeQuestion) {
      await redis.del('question:one-time-789');
      console.log('✅ Retrieved and deleted one-time question (simulating getAndDelete)');
    }

    // Verify it's deleted
    const deletedCheck = await redis.get('question:one-time-789');
    console.log('✅ Verified deletion:', deletedCheck === null ? 'Successfully deleted' : 'Still exists');

    // Test cleanup simulation
    console.log('\n🧪 Testing cleanup simulation...');
    
    // Create old question (simulate old timestamp)
    const oldQuestionData = {
      ...questionData,
      createdAt: Date.now() - (2 * 60 * 60 * 1000) // 2 hours ago
    };

    await redis.setEx('question:old-456', 3600, JSON.stringify(oldQuestionData));
    console.log('✅ Created old question for cleanup test');

    // List all question keys
    const keys = await redis.keys('question:*');
    console.log('✅ Found question keys:', keys);

    // Simulate cleanup logic
    let deletedCount = 0;
    const oneHour = 60 * 60 * 1000;
    
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) {
        const questionData = JSON.parse(data);
        const now = Date.now();
        
        if (now - questionData.createdAt > oneHour) {
          await redis.del(key);
          deletedCount++;
          console.log('🗑️  Deleted expired question:', key);
        }
      }
    }
    
    console.log('✅ Cleanup completed. Deleted', deletedCount, 'expired questions');

    // Clean up test data
    await redis.del('test:key');
    await redis.del('question:test-123');
    console.log('✅ Cleaned up test data');

    // Get Redis info
    console.log('\n📊 Redis Information:');
    const info = await redis.info('memory');
    const memoryLines = info.split('\r\n').filter(line => 
      line.includes('used_memory_human') || 
      line.includes('used_memory_peak_human') ||
      line.includes('maxmemory_human')
    );
    memoryLines.forEach(line => console.log('  ', line));

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Redis test failed:', error);
    process.exit(1);
  } finally {
    await redis.quit();
    console.log('🔌 Disconnected from Redis');
  }
}

// Run the test
testRedis().catch(console.error);
