'use client';

import { SessionProvider } from 'next-auth/react';
import { AuthProvider } from '@/contexts/AuthContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import PageTransition from '@/components/PageTransition';

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  // Allow disabling page transitions via environment variable or for debugging
  const enableTransitions = process.env.NEXT_PUBLIC_ENABLE_PAGE_TRANSITIONS !== 'false';

  return (
    <SessionProvider>
      <AuthProvider>
        <LanguageProvider>
          {enableTransitions ? (
            <PageTransition>
              {children}
            </PageTransition>
          ) : (
            children
          )}
        </LanguageProvider>
      </AuthProvider>
    </SessionProvider>
  );
}
