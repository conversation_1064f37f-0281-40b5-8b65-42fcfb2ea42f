'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function PrivacyPage() {
  const { t, language } = useLanguage();

  const getContent = () => {
    switch (language) {
      case 'vi':
        return {
          title: "Chính sách bảo mật",
          subtitle: "Chúng tôi cam kết bảo vệ quyền riêng tư và thông tin cá nhân của bạn",
          lastUpdated: "Cập nhật lần cuối: 1 tháng 1, 2025",
          sections: [
            {
              title: "1. Thông tin chúng tôi thu thập",
              content: "Chúng tôi có thể thu thập các loại thông tin sau:\n• Thông tin cá nhân: tên, địa chỉ email khi bạn tạo tài khoản\n• <PERSON><PERSON> liệu học tập: tiến độ học tập, kết quả quiz, thời gian học"
            },
            {
              title: "2. <PERSON>ách chúng tôi sử dụng thông tin",
              content: "Chúng tôi sử dụng thông tin của bạn để:\n• Cung cấp và cải thiện dịch vụ học tập\n• Cá nhân hóa trải nghiệm học tập\n• Theo dõi tiến độ và hiệu suất học tập\n• Gửi thông báo về dịch vụ (nếu bạn đồng ý)\n• Phân tích và cải thiện trang web\n• Đảm bảo an ninh và ngăn chặn gian lận"
            },
            {
              title: "3. Chia sẻ thông tin",
              content: "Chúng tôi không bán, trao đổi hoặc cho thuê thông tin cá nhân của bạn cho bên thứ ba"
            },
            {
              title: "4. Bảo mật dữ liệu",
              content: "Chúng tôi thực hiện các biện pháp bảo mật kỹ thuật và tổ chức phù hợp để bảo vệ thông tin cá nhân của bạn khỏi việc truy cập, sử dụng, tiết lộ, thay đổi hoặc phá hủy trái phép"
            },
            {
              title: "5. Lưu trữ dữ liệu",
              content: "Chúng tôi lưu trữ thông tin cá nhân của bạn chỉ trong thời gian cần thiết để thực hiện các mục đích đã nêu trong chính sách này hoặc theo yêu cầu của pháp luật. Khi bạn xóa tài khoản, chúng tôi sẽ xóa hoặc ẩn danh hóa dữ liệu cá nhân của bạn."
            },
            {
              title: "6. Thay đổi chính sách",
              content: "Chúng tôi có thể cập nhật chính sách bảo mật này theo thời gian. Chúng tôi sẽ thông báo về các thay đổi quan trọng bằng cách đăng chính sách mới trên trang web và cập nhật ngày \"Cập nhật lần cuối\" ở đầu trang."
            },
            {
              title: "7. Liên hệ",
              content: "Nếu bạn có bất kỳ câu hỏi nào về chính sách bảo mật này hoặc muốn thực hiện quyền của mình, vui lòng liên hệ với chúng tôi qua:\nEmail: <EMAIL>"
            }
          ]
        };
      case 'zh':
        return {
          title: "隐私政策",
          subtitle: "我们致力于保护您的隐私和个人信息",
          lastUpdated: "最后更新：2025年1月1日",
          sections: [
            {
              title: "1. 我们收集的信息",
              content: "我们可能收集以下类型的信息：\n• 个人信息：创建账户时的姓名、电子邮件地址\n• 学习数据：学习进度、测验结果、学习时间"
            },
            {
              title: "2. 我们如何使用信息",
              content: "我们使用您的信息来：\n• 提供和改进学习服务\n• 个性化学习体验\n• 跟踪学习进度和表现\n• 发送服务通知（如果您同意）\n• 分析和改进网站\n• 确保安全并防止欺诈"
            },
            {
              title: "3. 信息共享",
              content: "我们不会向第三方出售、交换或出租您的个人信息"
            },
            {
              title: "4. 数据安全",
              content: "我们实施适当的技术和组织安全措施，保护您的个人信息免受未经授权的访问、使用、披露、更改或销毁"
            },
            {
              title: "5. 数据存储",
              content: "我们只在实现本政策中所述目的或法律要求的必要时间内存储您的个人信息。当您删除账户时，我们将删除或匿名化您的个人数据。"
            },
            {
              title: "6. 政策变更",
              content: "我们可能会不时更新此隐私政策。我们将通过在网站上发布新政策并更新页面顶部的\"最后更新\"日期来通知重要变更。"
            },
            {
              title: "7. 联系我们",
              content: "如果您对此隐私政策有任何疑问或想要行使您的权利，请通过以下方式联系我们：\n电子邮件：<EMAIL>"
            }
          ]
        };
      default:
        return {
          title: "Privacy Policy",
          subtitle: "We are committed to protecting your privacy and personal information",
          lastUpdated: "Last updated: January 1, 2025",
          sections: [
            {
              title: "1. Information We Collect",
              content: "We may collect the following types of information:\n• Personal information: name, email address when you create an account\n• Learning data: learning progress, quiz results, study time"
            },
            {
              title: "2. How We Use Information",
              content: "We use your information to:\n• Provide and improve learning services\n• Personalize learning experience\n• Track learning progress and performance\n• Send service notifications (if you agree)\n• Analyze and improve the website\n• Ensure security and prevent fraud"
            },
            {
              title: "3. Information Sharing",
              content: "We do not sell, trade, or rent your personal information to third parties"
            },
            {
              title: "4. Data Security",
              content: "We implement appropriate technical and organizational security measures to protect your personal information from unauthorized access, use, disclosure, alteration, or destruction"
            },
            {
              title: "5. Data Storage",
              content: "We store your personal information only for as long as necessary to fulfill the purposes outlined in this policy or as required by law. When you delete your account, we will delete or anonymize your personal data."
            },
            {
              title: "6. Policy Changes",
              content: "We may update this privacy policy from time to time. We will notify you of significant changes by posting the new policy on the website and updating the \"Last updated\" date at the top of the page."
            },
            {
              title: "7. Contact Us",
              content: "If you have any questions about this privacy policy or want to exercise your rights, please contact us at:\nEmail: <EMAIL>"
            }
          ]
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {content.title}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
              {content.subtitle}
            </p>
            <p className="text-sm text-gray-500">
              {content.lastUpdated}
            </p>
          </div>

          {/* Content */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="space-y-8">
              {content.sections.map((section, index) => (
                <div key={index}>
                  <h2 className="text-xl font-bold text-gray-900 mb-4">
                    {section.title}
                  </h2>
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                    {section.content}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-12 text-center">
            <div className="bg-green-50 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {language === 'vi' ? 'Bảo vệ quyền riêng tư của bạn' : 
                 language === 'zh' ? '保护您的隐私' : 
                 'Protecting Your Privacy'}
              </h3>
              <p className="text-gray-600 mb-6">
                {language === 'vi' ? 'Chúng tôi cam kết bảo vệ thông tin cá nhân của bạn. Nếu có bất kỳ thắc mắc nào, hãy liên hệ với chúng tôi.' :
                 language === 'zh' ? '我们致力于保护您的个人信息。如有任何疑问，请联系我们。' :
                 'We are committed to protecting your personal information. If you have any questions, please contact us.'}
              </p>
              <a
                href="/contact"
                className="inline-block bg-green-600 text-white font-semibold px-8 py-3 rounded-xl hover:bg-green-700 transition-colors"
              >
                {language === 'vi' ? 'Liên hệ về quyền riêng tư' :
                 language === 'zh' ? '隐私相关联系' :
                 'Privacy Contact'}
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
