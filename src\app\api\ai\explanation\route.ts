import { NextRequest, NextResponse } from 'next/server';
import { generateExplanationWithGemini } from '@/lib/gemini';
import { Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { 
      question, 
      language = 'en', 
      context,
      explanationType = 'grammar'
    } = await request.json();

    if (!question || question.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Question is required' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    const validTypes = ['grammar', 'vocabulary', 'pronunciation', 'usage', 'cultural', 'general'];
    if (!validTypes.includes(explanationType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid explanation type provided' },
        { status: 400 }
      );
    }

    // Generate explanation using Gemini AI
    const explanation = await generateExplanationWithGemini({
      question,
      language,
      context: context || '',
      explanationType
    });

    return NextResponse.json({
      success: true,
      explanation
    });
  } catch (error) {
    console.error('Error generating explanation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate explanation' },
      { status: 500 }
    );
  }
}
