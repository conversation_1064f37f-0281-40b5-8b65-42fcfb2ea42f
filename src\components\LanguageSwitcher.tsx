'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Language } from '@/types';

interface LanguageSwitcherProps {
  isScrolled?: boolean;
}

export default function LanguageSwitcher({ isScrolled = false }: LanguageSwitcherProps) {
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  const languages = [
    { code: 'vi' as Language, name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' },
    { code: 'zh' as Language, name: '中文', flag: '🇨🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  const handleLanguageChange = async (newLanguage: Language) => {
    if (newLanguage === language) {
      setIsOpen(false);
      return;
    }

    setIsChanging(true);
    setIsOpen(false);

    // Add a small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 300));

    setLanguage(newLanguage);
    setIsChanging(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isChanging}
        className={`group flex items-center space-x-3 rounded-xl px-4 py-2.5 shadow-sm hover:shadow-md transition-all duration-500 backdrop-blur-sm disabled:opacity-75 disabled:cursor-not-allowed ${
          isScrolled
            ? 'bg-white/80 hover:bg-white/90'
            : 'bg-white/80 hover:bg-white/90'
        }`}
      >
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200 ${
          isScrolled
            ? 'bg-gradient-to-br from-gray-100 to-gray-200'
            : 'bg-gradient-to-br from-gray-100 to-gray-200'
        }`}>
          {isChanging ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
          ) : (
            <span className="text-lg">{currentLanguage?.flag}</span>
          )}
        </div>
        <span className={`text-sm font-semibold hidden sm:block transition-colors duration-500 ${
          isScrolled ? 'text-gray-700' : 'text-gray-700'
        }`}>
          {currentLanguage?.name}
        </span>
        <svg
          className={`w-4 h-4 transition-all duration-500 ${
            isOpen
              ? `rotate-180 ${isScrolled ? 'text-gray-700' : 'text-gray-700'}`
              : `${isScrolled ? 'text-gray-500 group-hover:text-gray-700' : 'text-gray-500 group-hover:text-gray-700'}`
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-48 bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl z-50">
          {languages.map((lang, index) => (
            <button
              key={lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-all duration-200 ${
                index === 0 ? 'rounded-t-2xl' : ''
              } ${
                index === languages.length - 1 ? 'rounded-b-2xl' : ''
              } ${
                language === lang.code
                  ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-l-4 border-blue-500'
                  : 'text-gray-700 hover:text-gray-900'
              }`}
            >
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                language === lang.code
                  ? 'bg-blue-100'
                  : 'bg-gray-100'
              }`}>
                <span className="text-lg">{lang.flag}</span>
              </div>
              <div className="flex-1">
                <span className="text-sm font-semibold">{lang.name}</span>
                {language === lang.code && (
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-xs text-blue-600">Active</span>
                  </div>
                )}
              </div>
              {language === lang.code && (
                <div className="flex-shrink-0">
                  <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
