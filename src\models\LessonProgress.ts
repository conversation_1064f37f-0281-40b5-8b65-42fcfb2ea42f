import mongoose, { Document, Schema } from 'mongoose';
import { LessonProgress, CourseProgress } from '@/types';

export interface ILessonProgress extends Omit<LessonProgress, 'userId' | 'lessonId'>, Document {
  _id: string;
  userId: mongoose.Types.ObjectId;
  lessonId: string;
}

export interface ICourseProgress extends Omit<CourseProgress, 'userId' | 'courseId'>, Document {
  _id: string;
  userId: mongoose.Types.ObjectId;
  courseId: string;
}

const LessonProgressSchema = new Schema<ILessonProgress>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  lessonId: { type: String, required: true },
  completedContent: [{ type: String }], // content IDs
  score: { type: Number, default: 0 },
  timeSpent: { type: Number, default: 0 }, // in minutes
  isCompleted: { type: Boolean, default: false },
  lastAccessedAt: { type: Date, default: Date.now },
  attempts: { type: Number, default: 0 }
}, {
  timestamps: true
});

const CourseProgressSchema = new Schema<ICourseProgress>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: String, required: true },
  completedLessons: [{ type: String }],
  currentLesson: { type: String },
  overallScore: { type: Number, default: 0 },
  timeSpent: { type: Number, default: 0 },
  isCompleted: { type: Boolean, default: false },
  startedAt: { type: Date, default: Date.now },
  lastAccessedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Indexes for better performance
LessonProgressSchema.index({ userId: 1, lessonId: 1 }, { unique: true });
LessonProgressSchema.index({ userId: 1 });
LessonProgressSchema.index({ lessonId: 1 });

CourseProgressSchema.index({ userId: 1, courseId: 1 }, { unique: true });
CourseProgressSchema.index({ userId: 1 });
CourseProgressSchema.index({ courseId: 1 });

export const LessonProgressModel = mongoose.models.LessonProgress || mongoose.model<ILessonProgress>('LessonProgress', LessonProgressSchema);
export const CourseProgressModel = mongoose.models.CourseProgress || mongoose.model<ICourseProgress>('CourseProgress', CourseProgressSchema);
