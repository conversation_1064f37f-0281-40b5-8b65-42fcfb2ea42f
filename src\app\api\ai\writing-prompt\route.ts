import { NextRequest, NextResponse } from 'next/server';
import { generateWritingPromptWithGemini } from '@/lib/gemini';
import { Level, Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { level, language = 'en', topic, type = 'essay' } = await request.json();

    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    const validTypes = ['essay', 'letter', 'email', 'story', 'description', 'opinion'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid writing type provided' },
        { status: 400 }
      );
    }

    // Generate writing prompt using Gemini AI
    const prompt = await generateWritingPromptWithGemini(level, language, topic, type);

    return NextResponse.json({
      success: true,
      prompt
    });
  } catch (error) {
    console.error('Error generating writing prompt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate writing prompt' },
      { status: 500 }
    );
  }
}
